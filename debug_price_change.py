#!/usr/bin/env python3
"""
Debug script to verify 2-month price change feature calculation
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta

def test_price_change_calculation():
    """Test the 2-month price change calculation independently"""
    print("=== Testing 2-Month Price Change Calculation ===")
    
    # Fetch SPY data for testing
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)  # Get 1 year of data
    
    print(f"Fetching SPY data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    try:
        spy_data = yf.download('SPY', start=start_date, end=end_date, progress=False)
        print(f"Downloaded {len(spy_data)} rows of SPY data")
        
        if len(spy_data) < 42:
            print(f"ERROR: Not enough data for 2-month calculation. Got {len(spy_data)} rows, need at least 42")
            return False
            
        # Calculate 2-month price change (42 trading days)
        close_prices = spy_data['Close']
        price_change_2m = close_prices.pct_change(periods=42)
        
        print(f"\n=== 2-Month Price Change Statistics ===")
        print(f"Total observations: {len(price_change_2m)}")
        print(f"Non-NaN observations: {price_change_2m.notna().sum()}")
        print(f"NaN observations: {price_change_2m.isna().sum()}")
        
        # Get valid (non-NaN) price changes
        valid_changes = price_change_2m.dropna()
        if len(valid_changes) > 0:
            print(f"\n=== Valid Price Change Statistics ===")
            print(f"Count: {len(valid_changes)}")
            print(f"Mean: {float(valid_changes.mean()):.6f}")
            print(f"Median: {float(valid_changes.median()):.6f}")
            print(f"Std: {float(valid_changes.std()):.6f}")
            print(f"Min: {float(valid_changes.min()):.6f}")
            print(f"Max: {float(valid_changes.max()):.6f}")

            # Show recent values
            print(f"\n=== Recent 2-Month Price Changes ===")
            recent_changes = valid_changes.tail(10)
            for date, change in recent_changes.items():
                print(f"{date.strftime('%Y-%m-%d')}: {float(change):.6f} ({float(change)*100:.2f}%)")
                
            # Check for all-zero values
            zero_count = (valid_changes == 0).sum()
            print(f"\n=== Data Quality Check ===")
            print(f"Zero values: {zero_count}")
            print(f"Near-zero values (abs < 1e-6): {(valid_changes.abs() < 1e-6).sum()}")
            
            # Test if feature would be useful for ML
            if valid_changes.std() > 0.001:  # Some reasonable variation
                print("✅ Feature appears to have meaningful variation for ML")
            else:
                print("⚠️  Feature has very low variation - may not be useful for ML")
                
            return True
        else:
            print("ERROR: No valid price change values calculated")
            return False
            
    except Exception as e:
        print(f"ERROR: Failed to test price change calculation: {e}")
        return False

def test_spy_py_integration():
    """Test if the feature would be correctly processed by SPY.py logic"""
    print("\n=== Testing SPY.py Integration Logic ===")
    
    # Simulate the SPY.py processing logic
    try:
        # Create sample data similar to what SPY.py processes
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        # Simulate SPY close prices with some realistic variation
        np.random.seed(42)  # For reproducible results
        base_price = 400
        price_changes = np.random.normal(0, 0.01, len(dates))  # 1% daily volatility
        prices = [base_price]
        for change in price_changes[1:]:
            prices.append(prices[-1] * (1 + change))
        
        df = pd.DataFrame({
            'close_SPY': prices
        }, index=dates)
        
        print(f"Created test DataFrame with {len(df)} rows")
        
        # Apply the same logic as SPY.py
        clean_ticker = 'SPY'
        close_col = 'close_SPY'
        
        # Calculate 2-month price change (approximately 42 trading days)
        if len(df) >= 42:  # 2-month price change needs 42 data points
            try:
                df[f'price_change_2m_{clean_ticker}'] = df[close_col].pct_change(periods=42)
                print("✅ Successfully calculated price_change_2m_SPY")
                
                # Check the results
                price_change_col = f'price_change_2m_{clean_ticker}'
                valid_changes = df[price_change_col].dropna()
                
                print(f"Valid price changes: {len(valid_changes)}")
                print(f"Mean: {valid_changes.mean():.6f}")
                print(f"Std: {valid_changes.std():.6f}")
                
                # Check if it would be included in feature_columns_to_keep
                feature_columns_to_keep = [f'atr_{clean_ticker}', f'rsi_{clean_ticker}', 
                                         f'sma_{clean_ticker}', f'sma50_{clean_ticker}', 
                                         f'price_change_2m_{clean_ticker}']
                
                available_cols = [col for col in feature_columns_to_keep if col in df.columns]
                print(f"Columns that would be kept: {available_cols}")
                
                if price_change_col in available_cols:
                    print("✅ price_change_2m_SPY would be included in final features")
                else:
                    print("❌ price_change_2m_SPY would NOT be included in final features")
                    
                return True
                
            except Exception as e_2m:
                print(f"❌ Error calculating 2-month price change: {e_2m}")
                return False
        else:
            print(f"❌ Not enough data for 2-month price change. Got {len(df)} rows, need 42")
            return False
            
    except Exception as e:
        print(f"ERROR: Failed to test SPY.py integration: {e}")
        return False

if __name__ == "__main__":
    print("Starting 2-Month Price Change Feature Debug\n")
    
    success1 = test_price_change_calculation()
    success2 = test_spy_py_integration()
    
    print(f"\n=== Summary ===")
    print(f"Real data test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"SPY.py integration test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n✅ 2-month price change feature appears to be working correctly!")
        print("If you're still seeing issues, the problem might be:")
        print("1. Data quality issues in the actual dataset")
        print("2. The feature values are very small and appear as zeros in logs")
        print("3. The feature is being processed but not used effectively by the model")
    else:
        print("\n❌ Issues detected with 2-month price change feature")
